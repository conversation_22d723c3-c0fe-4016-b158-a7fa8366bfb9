use crate::config::TunConfig;
use anyhow::Result;
use log::{info, error};
use tokio::io::{AsyncRead, AsyncWrite};
use tun::{AsyncDevice, Configuration};

/// TUN device manager
pub struct TunDeviceManager {
    config: TunConfig,
}

impl TunDeviceManager {
    /// Create a new TUN device manager
    pub fn new(config: TunConfig) -> Self {
        Self { config }
    }

    /// Create and configure the TUN device
    pub async fn create_device(&self) -> Result<AsyncDevice> {
        let mut tun_config = Configuration::default();
        tun_config
            .address(self.config.address)
            .netmask(self.config.netmask)
            .destination(self.config.destination)
            .up();

        let device = tun::create_as_async(&tun_config)
            .map_err(|e| anyhow::anyhow!("Failed to create TUN device: {}", e))?;

        info!("TUN device created successfully");
        info!("  Address: {}", self.config.address);
        info!("  Netmask: {}", self.config.netmask);
        info!("  Destination: {}", self.config.destination);

        Ok(device)
    }
}

/// TUN device wrapper for easier management
pub struct TunDevice {
    device: AsyncDevice,
}

impl TunDevice {
    /// Create a new TUN device wrapper
    pub fn new(device: AsyncDevice) -> Self {
        Self { device }
    }

    /// Split the device into reader and writer
    pub fn split(self) -> (impl AsyncRead + Unpin, impl AsyncWrite + Unpin) {
        tokio::io::split(self.device)
    }
}

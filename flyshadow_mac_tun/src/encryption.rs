use crate::config::EncryptionConfig;
use chacha20::cipher::{KeyIvInit, StreamCipher};
use chacha20::ChaCha20;
use log::{debug, error};

/// Encryption manager for packet encryption/decryption
#[derive(Clone)]
pub struct EncryptionManager {
    config: EncryptionConfig,
}

impl EncryptionManager {
    /// Create a new encryption manager
    pub fn new(config: EncryptionConfig) -> Self {
        Self { config }
    }

    /// Encrypt data in place using ChaCha20
    pub fn encrypt(&self, data: &mut [u8]) -> Result<(), EncryptionError> {
        debug!("Encrypting {} bytes", data.len());
        
        let mut cipher = ChaCha20::new(
            &self.config.key.into(),
            &self.config.nonce.into()
        );
        
        cipher.apply_keystream(data);
        Ok(())
    }

    /// Decrypt data in place using ChaCha20 (same as encrypt for symmetric cipher)
    pub fn decrypt(&self, data: &mut [u8]) -> Result<(), EncryptionError> {
        debug!("Decrypting {} bytes", data.len());
        
        // ChaCha20 is symmetric, so decryption is the same as encryption
        self.encrypt(data)
    }

    /// Encrypt data and return a new vector
    pub fn encrypt_to_vec(&self, data: &[u8]) -> Result<Vec<u8>, EncryptionError> {
        let mut encrypted = data.to_vec();
        self.encrypt(&mut encrypted)?;
        Ok(encrypted)
    }

    /// Decrypt data and return a new vector
    pub fn decrypt_to_vec(&self, data: &[u8]) -> Result<Vec<u8>, EncryptionError> {
        let mut decrypted = data.to_vec();
        self.decrypt(&mut decrypted)?;
        Ok(decrypted)
    }
}

/// Encryption-related errors
#[derive(Debug, thiserror::Error)]
pub enum EncryptionError {
    #[error("Encryption failed: {0}")]
    EncryptionFailed(String),
    #[error("Decryption failed: {0}")]
    DecryptionFailed(String),
    #[error("Invalid key or nonce")]
    InvalidKeyOrNonce,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_decryption() {
        let config = EncryptionConfig {
            key: *b"01234567890123456789012345678901",
            nonce: *b"flyshadow123",
        };
        
        let manager = EncryptionManager::new(config);
        let original_data = b"Hello, World! This is a test message.";
        
        // Test in-place encryption/decryption
        let mut data = original_data.to_vec();
        manager.encrypt(&mut data).unwrap();
        
        // Data should be different after encryption
        assert_ne!(data, original_data);
        
        // Decrypt should restore original data
        manager.decrypt(&mut data).unwrap();
        assert_eq!(data, original_data);
    }

    #[test]
    fn test_encrypt_to_vec() {
        let config = EncryptionConfig {
            key: *b"01234567890123456789012345678901",
            nonce: *b"flyshadow123",
        };
        
        let manager = EncryptionManager::new(config);
        let original_data = b"Hello, World!";
        
        let encrypted = manager.encrypt_to_vec(original_data).unwrap();
        assert_ne!(encrypted, original_data);
        
        let decrypted = manager.decrypt_to_vec(&encrypted).unwrap();
        assert_eq!(decrypted, original_data);
    }
}
